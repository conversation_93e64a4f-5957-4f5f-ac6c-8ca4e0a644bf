<template>
  <div class="contact-page">
    <div class="contact-container">
      <div class="contact-left">
        <div class="right-section">
          <h2>企业服务特色</h2>
          <ul class="feature-list">
            <li>AI智能批改，效率提升90%</li>
            <li>软硬件一体化，支持多种场景</li>
            <li>错题本自动生成，精准学情分析</li>
          </ul>
        </div>
        <div class="right-section">
          <h2>已服务客户</h2>
          <p class="right-desc">已覆盖杭州、上海、佛山、重庆、南京等多地学校，累计批改超22万份试卷。</p>
          <div class="right-logos">
            <div class="logo-row">
              <img src="/images/logo1.png" alt="logo1" />
              <img src="/images/校徽_01.png" alt="校徽1" />
              <img src="/images/logo2.png" alt="logo2" />
              <img src="/images/校徽_02.png" alt="校徽2" />
              <img src="/images/logo3.png" alt="logo3" />
            </div>
            <div class="logo-row">
              <img src="/images/校徽_03.png" alt="校徽3" />
              <img src="/images/logo4.png" alt="logo4" />
              <img src="/images/校徽_04.png" alt="校徽4" />
              <img src="/images/logo5.png" alt="logo5" />
              <img src="/images/校徽_05.png" alt="校徽5" />
            </div>
            <div class="logo-row">
              <img src="/images/logo6.png" alt="logo6" />
              <img src="/images/校徽_06.png" alt="校徽6" />
              <img src="/images/logo7.png" alt="logo7" />
              <img src="/images/校徽_07.png" alt="校徽7" />
              <img src="/images/校徽_08.png" alt="校徽8" />
            </div>
          </div>
          <button class="card-button primary" @click="showContactModal = true">立即咨询</button>
        </div>
      </div>
      <div class="contact-right">
        <div class="contact-header">
          <img src="/images/miaomiaowangwang.png" alt="喵喵汪汪" class="contact-logo" />
          <h1>联系我们</h1>
          <p class="contact-desc">欢迎咨询，我们将为您定制最合适的解决方案！</p>
        </div>
        <form class="contact-form" @submit.prevent="handleSubmit">
          <div class="form-group">
            <label for="name">姓名*</label>
            <input id="name" v-model="form.name" required placeholder="请输入您的姓名" />
          </div>
          <div class="form-group">
            <label for="email">邮箱*</label>
            <input id="email" v-model="form.email" type="email" required placeholder="请输入您的邮箱" />
          </div>
          <div class="form-group">
            <label for="phone">电话*</label>
            <input id="phone" v-model="form.phone" required placeholder="请输入您的联系电话" />
          </div>
          <div class="form-group">
            <label for="company">单位/学校*</label>
            <input id="company" v-model="form.company" required placeholder="请输入单位或学校名称" />
          </div>
          <div class="form-group">
            <label for="message">留言</label>
            <textarea id="message" v-model="form.message" rows="4" placeholder="请描述您的需求或问题"></textarea>
          </div>
          <button class="btn-primary" type="submit">提交</button>
        </form>
        <div class="form-tip">*必填项，您的信息仅用于沟通，不会泄露给第三方。</div>
      </div>
    </div>
    <ContactModal :visible="showContactModal" @close="showContactModal = false" />
    <div v-if="message" :class="['custom-message', messageType]">{{ message }}</div>
  </div>
</template>

<script>
import ContactModal from './ContactModal.vue'

export default {
  name: 'Contact',
  components: {
    ContactModal
  },
  data() {
    return {
      form: {
        name: '',
        email: '',
        phone: '',
        company: '',
        message: '',
      },
      showContactModal: false,
      message: '',
      messageType: '', // 'success' or 'error'
    };
  },
  methods: {
    showMessage(msg, type = 'success') {
      this.message = msg;
      this.messageType = type;
      setTimeout(() => {
        this.message = '';
        this.messageType = '';
      }, 3000);
    },
    handleSubmit() {
      // 前端校验
      if (!this.form.name.trim()) {
        this.showMessage('请输入姓名', 'error');
        return;
      }
      const emailPattern = /^[^@\s]+@[^@\s]+\.[^@\s]+$/;
      if (!emailPattern.test(this.form.email)) {
        this.showMessage('请输入有效的邮箱地址', 'error');
        return;
      }
      const phonePattern = /^1[3-9]\d{9}$/;
      if (!phonePattern.test(this.form.phone)) {
        this.showMessage('请输入有效的手机号', 'error');
        return;
      }
      // 提交到后端接口
      const payload = {
        name: this.form.name,
        email: this.form.email,
        phone: this.form.phone,
        schoolName: this.form.company,
        message: this.form.message
      };
      fetch('/api/contact/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })
        .then(async res => {
          const data = await res.json();
          if (data.code === 200) {
            this.showMessage('提交成功！我们会尽快与您联系。', 'success');
            this.form = { name: '', email: '', phone: '', company: '', message: '' };
          } else {
            this.showMessage('提交失败：' + (data.message || '请稍后重试'), 'error');
          }
        })
        .catch(err => {
          this.showMessage('提交失败：' + err.message, 'error');
        });
    },
  },
};
</script>

<style scoped>
.contact-page {
  background: #fff;
  min-height: 100vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 60px 0 40px 0;
}
.contact-container {
  display: flex;
  max-width: 1100px;
  width: 100%;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(96,166,255,0.10);
  overflow: hidden;
}
.contact-left {
  flex: 1;
  padding: 48px 36px 40px 36px;
  background: #fff url('/images/AI批改.png') center center / cover no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 36px;
  border-right: 1.5px solid #e3eaf6;
  position: relative;
}
.contact-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.85);
  z-index: 1;
}
.contact-left > * {
  position: relative;
  z-index: 2;
}
.contact-header {
  text-align: center;
  margin-bottom: -12px;
}
.contact-logo {
  width: 64px;
  height: 64px;
  object-fit: contain;
  margin-bottom: 12px;
}
.contact-header h1 {
  color: rgb(96,166,255);
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 8px 0;
}
.contact-desc {
  color: #6c757d;
  font-size: 1.1rem;
}
.contact-form {
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.form-group label {
  font-size: 1.5rem;
  color: #1c1c1c;
  font-weight: 500;
  text-align: left;
}
.form-group input,
.form-group textarea {
  padding: 10px 14px;
  border: 1.5px solid #dbeafe;
  border-radius: 6px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s;
  background: #fff;
}
.form-group input:focus,
.form-group textarea:focus {
  border-color: rgb(96,166,255);
}
.btn-primary {
  background: rgb(96,166,255);
  color: #fff;
  border: none;
  padding: 16px 0;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 8px;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(96,166,255,0.08);
  width: 100%;
}
.btn-primary:hover {
  background: rgb(76,146,235);
}
.form-tip {
  margin-top: 18px;
  color: #6c757d;
  font-size: 0.95rem;
  text-align: center;
}
.contact-right {
  flex: 1.2;
  padding: 32px 40px 32px 40px;
  background: #f8faff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.right-section h2 {
  color: rgb(96,166,255);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 12px;
}
.feature-list {
  list-style: none;
  padding: 0;
  margin: 0 0 12px 0;
}
.feature-list li {
  color: #1c1c1c;
  font-size: 1.5rem;
  margin-bottom: 8px;
  padding-left: 2em;
  position: relative;
  text-align: left;
}
.feature-list li::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background: rgb(96,166,255);
  border-radius: 50%;
  position: absolute;
  left: 0;
  top: 0.5em;
}
.right-logos {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  margin-top: 10px;
}
.logo-row {
  display: flex;
  gap: 16px;
  justify-content: center;
}
.right-logos img {
  width: 72px;
  height: 72px;
  object-fit: contain;
  border-radius: 8px;
  background: #f8faff;
  border: 1px solid #e3eaf6;
}
.card-button {
  width: 100%;
  padding: 16px;
  border: 2px solid rgb(96,166,255);
  background: transparent;
  color: rgb(96,166,255);
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: 20px;
}
.card-button:hover {
  background: rgb(96,166,255);
  color: white;
}
.card-button.primary {
  background: rgb(96,166,255);
  color: white;
}
.card-button.primary:hover {
  background: rgb(76,146,235);
}
@media (max-width: 900px) {
  .contact-container {
    flex-direction: column;
    box-shadow: none;
  }
  .contact-left, .contact-right {
    padding: 32px 16px;
    border-radius: 0;
    border: none;
  }
  .contact-left {
    border-right: none;
  }
  .contact-right {
    border-top: 1.5px solid #e3eaf6;
  }
}
.right-desc {
  font-size: 1.5rem;
  text-align: left;
}
.custom-message {
  position: fixed;
  top: 32px;
  left: 50%;
  transform: translateX(-50%);
  min-width: 260px;
  max-width: 90vw;
  padding: 18px 32px;
  border-radius: 8px;
  font-size: 1.15rem;
  font-weight: 600;
  color: #fff;
  z-index: 9999;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  text-align: center;
  transition: opacity 0.3s;
}
.custom-message.success {
  background: #3ecb6c;
}
.custom-message.error {
  background: #e74c3c;
}
</style> 